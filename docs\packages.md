## 数据接入网关

https://flows.nodered.org/node/node-red-contrib-iiot-opcua
https://flows.nodered.org/node/node-red-contrib-modbustcp

node-red-contrib-ftp-server

node-red-contrib-betterplus-sftp

npm install node-red-contrib-advanced-ftp

|                      节点ID                      | 节点描述                                                     | 状态 |
| :----------------------------------------------: | :----------------------------------------------------------- | ------------------------------------------------------------ |
|         @meowwolf/node-red-contrib-amqp          | AMQP nodes                                                   | 使用中                                                       |
|          @serdoo/node-red-contrib-soap           | SOAP nodes                                                   | 使用中                                                       |
|               node-postgres-named                | Named parameters for node-postgres                           | 使用中                                                       |
|                  node-red-admin                  | Node-RED admin command line interface                        | 使用中                                                       |
|            node-red-configurable-ping            | Takes input and pings a remote server                        | 使用中                                                       |
|         node-red-contrib-chunks-to-lines         | Read line by line from chunks of text                        | 使用中                                                       |
|            node-red-contrib-dir2files            | Searches files in a directory and return paths which can be filtered by regex | 使用中                                                       |
|           node-red-contrib-filesystem            | Work with filesystem. <br />You can copy, move, link, delete files, create, list and remove folders and get info | 使用中                                                       |
|               node-red-contrib-fs                | Handle the host filing system                                | 使用中                                                       |
|             node-red-contrib-fs-ops              | Perform basic file system operations                         |                                                              |
| node-red-contrib-full-msg-json-schema-validation | JSON Schema validator                                        | 使用中                                                       |
|             node-red-contrib-graphql             | Make GraphQL calls                                           | 使用中                                                       |
|              node-red-contrib-iconv              | Based on iconv-lite for encoding or decoding conversion to pure JS encoding | 使用中                                                       |
|           node-red-contrib-iiot-opcua            | Read a file and write to a file in the charset that you want (not only utf8) | 使用中                                                       |
|            node-red-contrib-jsonpath             | Evaluate JSONPath expressions over the messages received     | 使用中                                                       |
|         node-red-contrib-lodash-throttle         | The lodash method _.throttle                                 | 使用中                                                       |
|         node-red-contrib-loop-processing         | Help flow looping                                            | 使用中                                                       |
|               node-red-contrib-md5               | Calculate an MD5 hash for a message property                 | 使用中                                                       |
|             node-red-contrib-md5file             | Calculate an MD5 hash for a give file                        |                                                              |
|            node-red-contrib-modbustcp            | Communicating with a MODBUS TCP server                       | 使用中                                                       |
|           node-red-contrib-mqtt-broker           | MQTT Broker server                                           | 使用中                                                       |
|           node-red-contrib-mssql-plus            | Execute queries, stored procedures and bulk inserts.<br />Microsoft SQL Server and Azure Databases SQL2000 ~ SQL2019 | 使用中                                                       |
|      @ais_automation/node-red-contrib-odbc       | To unixODBC and its supported drivers                        | 使用中                                                       |
|           node-red-contrib-postgresql            | For PostgreSQL, supporting parameters, split, back-pressure  | 使用中                                                       |
|             node-red-contrib-readdir             | List the files in a directory                                | 使用中                                                       |
|              node-red-contrib-redis              | Client for Redis with pub/sub, list, lua scripting and other commands support | 使用中                                                       |
|          node-red-contrib-simplest-soap          | The simplest SOAP wrapper                                    | 使用中                                                       |
|            node-red-contrib-socketio             | A SocketIO Sever                                             | 使用中                                                       |
|            node-red-contrib-uibuilder            | Easily create data-driven web UI                             | 使用中                                                       |
|           node-red-contrib-webservices           | A soap webservices including server and client               | 使用中                                                       |
|           node-red-node-data-generator           | Create a string of dummy data values from a template. Useful for test-cases | 使用中                                                       |
|               node-red-node-mysql                | Read and write to a MySQL database                           | 使用中                                                       |
|                node-red-node-rbe                 | Provides report-by-exception (RBE) and deadband capabilities | 禁用                                                     |
|             node-red-node-sentiment              | Use the AFINN-165 wordlists for sentiment analysis of words  | 使用中                                                       |
|                node-red-node-tail                | Tail files                                                   | 使用中                                                       |

|      |      
-------|-------
|      |      
|      |      
|      |      
|      |      
|      |      
|      |      
|      |      
