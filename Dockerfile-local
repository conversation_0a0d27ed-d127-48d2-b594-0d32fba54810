# Build stage
FROM m.daocloud.io/docker.io/library/node:20-bookworm-slim AS builder

# System update and timezone config
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
# 配置阿里云镜像源
RUN sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources \
  && apt-get update \
  && apt-get install -y --no-install-recommends g++ make python3 \
  && ln -fs /usr/share/zoneinfo/$TZ /etc/localtime \
  && echo $TZ > /etc/timezone
# NPM registry config
RUN npm config set registry https://registry.npmmirror.com

# Copy package files
WORKDIR /app
COPY . .

# Install dependencies for custom nodes first
WORKDIR /app/custom/@bdtd/js-common-polyfill
RUN npm install --production --no-optional

WORKDIR /app/custom/@purezhi/node-red-contrib-heapdump
RUN npm install --production --no-optional

WORKDIR /app/custom/@purezhi/node-red-contrib-kafkajs
RUN npm install --production --no-optional

# Install dependencies including node-red nodes
WORKDIR /app/profile
RUN cp package-template.json package.json \
  && npm install --omit=dev --legacy-peer-deps --no-update-notifier --no-fund --no-audit \
  && npm install --unsafe-perm ../custom/@bdtd/js-common-polyfill \
  && npm install --unsafe-perm ../custom/@purezhi/node-red-contrib-heapdump \
  && npm install --unsafe-perm ../custom/@purezhi/node-red-contrib-kafkajs

# Clean up unnecessary files and fix not existed files
RUN rm -rf node_modules/custom/*/test \
  && mv /app/docker/assets/tdengine.png /app/profile/node_modules/node-red-node-tdengine/tdengine.png

# ---------------------------------------------------------

# Production stage
FROM m.daocloud.io/docker.io/library/node:20-bookworm-slim
LABEL maintainer="Wang Zhiwei<<EMAIL>>"

# System update and timezone config
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Shanghai
# Install runtime dependencies
RUN sed -i '<EMAIL>@mirrors.aliyun.com@g' /etc/apt/sources.list.d/debian.sources \
  && apt-get update \
  && apt-get install -y --no-install-recommends cron logrotate \
  && ln -fs /usr/share/zoneinfo/$TZ /etc/localtime \
  && echo $TZ > /etc/timezone \
  && apt-get clean \
  && rm -rf /var/lib/apt/lists/*

# Copy from builder stage
WORKDIR /app
COPY --from=builder /app .

# Setup logrotate
COPY ./docker/pm2-apps /etc/logrotate.d/
# /usr/sbin/logrotate -d /etc/logrotate.d/pm2-apps
RUN chmod 644 /etc/logrotate.d/pm2-apps \
  && mkdir -p /var/lib/logrotate \
  && touch /var/lib/logrotate/status \
  && echo "*/1 * * * * root /usr/sbin/logrotate /etc/logrotate.conf" >> /etc/crontab

# Setup entrypoint
RUN mv /usr/local/bin/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh.bak
COPY ./docker/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Install PM2 globally as it's needed for the CMD instruction
RUN npm config set registry https://registry.npmmirror.com \
  && npm install -g --unsafe-perm --no-update-notifier --no-fund --no-audit node-red@4.0.9 \
  && npm install -g --no-update-notifier --no-fund --no-audit pm2 \
  && npm install fs-extra

# Set environment variables
ENV NODE_ENV=production
# Expose the listening port of your app
EXPOSE 1880
# Start the app
CMD ["pm2-runtime", "start", "ecosystem.config.js"]
