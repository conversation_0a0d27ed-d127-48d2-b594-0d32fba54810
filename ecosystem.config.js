// Options reference: https://pm2.io/doc/en/runtime/reference/ecosystem-file/
module.exports = {
    apps : [{
        name: 'node-gw',
        script: 'node-red --settings /app/settings.js',
        instances: 1,
        autorestart: true,
        watch: false,
        max_memory_restart: process.env.PM2_MAX_MEMORY_RESTART || '2G',
        
        // Restart strategy
        restart_delay: 3000,
        max_restarts: 10,
        min_uptime: '10s',
        
        // Graceful shutdown
        kill_timeout: 30000,
        listen_timeout: 10000,
        shutdown_with_message: true,
        
        // Logging configuration, default: /root/.pm2/logs
        log_file: '/app/logs/node-gw-combined.log',
        out_file: '/app/logs/node-gw-out.log',
        error_file: '/app/logs/node-gw-error.log',
        // log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
        // merge_logs: true,
        
        // Process management
        pid_file: '/root/.pm2/pids/node-gw.pid',
        
        env: {
            NODE_ENV: 'development',
            PORT: 1880
        },
        env_production: {
            NODE_ENV: 'production',
            PORT: 1880
        }
    }]
};
