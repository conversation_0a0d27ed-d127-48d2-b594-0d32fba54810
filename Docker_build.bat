@echo off
setlocal

set "TARGET_BRANCH=%~1"
set "CURRENT_BRANCH="
set "SWITCHED_BRANCH=0"

echo --------------------------------------------------------
echo Build docker image from dist
echo example: Docker_build.bat [branch] [tag] [comment]
echo --------------------------------------------------------

if "%TARGET_BRANCH%"=="" (
  echo Branch not assigned!
  echo Command: Docker_build.bat [branch] [tag] [comment]
  echo Example 1: Docker_build.bat dev-v4 dev-v4 测试提交
  echo Example 2: Docker_build.bat dev-v4 dev-v4
  echo Example 3: Docker_build.bat dev-v4
  pause
  goto:eof
)

REM get current branch name
for /f "tokens=*" %%g in ('git rev-parse --abbrev-ref HEAD') do (set "CURRENT_BRANCH=%%g")
if not defined CURRENT_BRANCH (
  echo.
  echo Failed to get current git branch. Make sure you are in a git repository.
  pause
  goto:eof
)

echo.
echo Current branch is: %CURRENT_BRANCH%
echo Target branch is: %TARGET_BRANCH%

set /p inputUbs="Press ENTER to update branch source, q to exit, others to continue: "
if "!inputUbs!"=="" (
  REM switch branch only if it's different
  if /I not "%CURRENT_BRANCH%"=="%TARGET_BRANCH%" (
    echo.
    echo Current branch is not the target branch. Switching to %TARGET_BRANCH%...
    pause
    git switch %TARGET_BRANCH%
    if !errorlevel! neq 0 (
      echo Failed to switch to branch %TARGET_BRANCH%.
      pause
      goto:cleanup
    )
    set "SWITCHED_BRANCH=1"
  ) else (
    echo.
    echo Already on the target branch.
  )

  echo.
  echo Continue to update "%TARGET_BRANCH%" branch...
  pause

  REM update source
  git pull
) else (
  if "!inputUbs!"=="q" (
    goto:eof
  )
)


REM build 镜像,根据在gitlab中ci文档的harbor路径，修改此处path
echo.
echo Continue to build image...
pause

REM not assign tag, only tag the current branch
if "%~2"=="" (
  set /p inputNbdi="Press ENTER to build the Docker image, q to exit, others to continue: "
  if "!inputNbdi!"=="" (
    REM Build docker image
    docker build -f Dockerfile -t harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:"%TARGET_BRANCH%" .
  ) else (
    if "!inputNbdi!"=="q" (
      goto:eof
    )
  )

  set /p inputNpih=" Press ENTER to push the image to Harbor, q to exit, others to continue:"
  if "!inputNpih!"=="" (
    REM Push image to harbor
    docker push harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:"%TARGET_BRANCH%"  
  ) else (
    if "!inputNpih!"=="q" (
      goto:eof
    )
  )
) else (
  set /p inputTbdi="Press ENTER to build the Docker image, q to exit, others to continue: "
  if "!inputTbdi!"=="" (
    REM Build docker image
    docker build -f Dockerfile -t harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:"%~2" .
  ) else (
    if "!inputTbdi!"=="q" (
      goto:eof
    )
  )

  set /p inputTpih="Press ENTER to push the image to Harbor, q to exit, others to continue: "
  if "!inputTpih!"=="" (
    REM Tag image's tag and branch
    docker tag harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:"%~2" harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:"%TARGET_BRANCH%"
    docker push harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:"%~2"
    docker push harbor2.qdbdtd.com:8088/bjproduct/platform/gateway/node-gw:"%TARGET_BRANCH%"
  ) else (
    if "!inputTpih!"=="q" (
      goto:eof
    )
  )

  set /p inputTptg="Press ENTER to push the tag to Gitee, q to exit, others to continue: "
  if "!inputTptg!"=="" (
    REM Push tag to Gitee
    git tag -a "%~2" -m "%~3"
    git push origin "%~2"
  ) else (
    if "!inputTptg!"=="q" (
      goto:eof
    )
  )
)

echo.
echo Image build success.

:cleanup
if "%SWITCHED_BRANCH%"=="1" (
  echo.
  echo Switching back to original branch: %CURRENT_BRANCH%
  git switch %CURRENT_BRANCH%
)

pause
endlocal
