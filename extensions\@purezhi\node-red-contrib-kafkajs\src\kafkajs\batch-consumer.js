/** @import { Node, NodeAPI, NodeInitializer } from 'node-red' */
/** @import { Consumer } from 'kafkajs' */
/** @import { KafkajsBrokerNode } from './broker' */

/**
 * Module to control the access to the broker.
 * @type {NodeInitializer}
 * @param {NodeAPI} RED Root node to register the components
 */
module.exports = function (RED) {
  const os = require('os');
  const crypto = require('node:crypto');

  /**
   * @typedef {Object} KafkajsBatchConsumerNode
   * @extends {Node}
   * */
  function KafkajsBatchConsumerNode(config) {
    /**
     * @type {KafkajsBatchConsumerNode}
     * @extends {Node}
     * */
    const node = this;

    RED.nodes.createNode(this, config);

    /** @type {Consumer} */
    this.consumer = null;

    /** @type {KafkajsBrokerNode} */
    const broker = RED.nodes.getNode(config.broker);
    if (!broker) {
      node.status({ fill: 'red', shape: 'ring', text: '<PERSON>roker is missing.' });
      return;
    }

    const consumerOptions = {
      /** @type {string} */
      groupId: config.groupid ? config.groupid : 'kafka_js_' + os.hostname() + '_' + crypto.randomBytes(4).toString('hex'),
    };

    const subscribeOptions = {
      /** @type {string} */
      topic: config.topic,
    };

    /**
     * Deserializes the message payload or key to a buffer.
     * @param {Buffer} msg The message received from the broker.
     * @param {string} toType The type inside the Buffer to convert the message from.
     * @returns {object | string | number | bigint} The message as the requested type.
     */
    function convertMessage(msg, toType) {
      if (!Buffer.isBuffer(msg)) {
        return msg;
      }

      try {
        if (toType === 'json') {
          return JSON.parse(msg.toString());
        } else if (toType === 'string') {
          return msg.toString();
        } else if (toType === 'boolean') {
          return msg.readUInt8() != 0;
        } else if (toType === 'int8') {
          return msg.readInt8();
        } else if (toType === 'int16be') {
          return msg.readInt16BE();
        } else if (toType === 'int16le') {
          return msg.readInt16LE();
        } else if (toType === 'int32be') {
          return msg.readInt32BE();
        } else if (toType === 'int32le') {
          return msg.readInt32LE();
        } else if (toType === 'int64be') {
          return msg.readBigInt64BE();
        } else if (toType === 'int64le') {
          return msg.readBigInt64LE();
        } else if (toType === 'uint8') {
          return msg.readUInt8();
        } else if (toType === 'uint16be') {
          return msg.readUInt16BE();
        } else if (toType === 'uint16le') {
          return msg.readUInt16LE();
        } else if (toType === 'uint32be') {
          return msg.readUInt32BE();
        } else if (toType === 'uint32le') {
          return msg.readUint32LE();
        } else if (toType === 'uint64be') {
          return msg.readBigUInt64BE();
        } else if (toType === 'uint64le') {
          return msg.readBigUInt64LE();
        } else if (toType === 'doublebe') {
          return msg.readDoubleBE();
        } else if (toType === 'doublele') {
          return msg.readDoubleLE();
        } else if (toType === 'floatbe') {
          return msg.readFloatBE();
        } else if (toType === 'floatle') {
          return msg.readFloatLE();
        }
      } catch (ex) {
        node.error(`Kafka Batch Consumer Error (message conversion): ${ex}`, ex);
      }

      return msg;
    }

    /** @type {boolean} */
    this.firstMessageReceived = false;
    /** @type {number} */
    this.lastCommitOffset = 0;

    /** @type {object} */
    const runOptions = {};

    // Just send the raw message form consumer
    // node.log(`config.sendrawmessage: ${config.sendrawmessage}`);
    if (config.sendrawmessage) {
      runOptions.eachBatch = async ({
        batch,
        resolveOffset,
        heartbeat,
        isRunning,
        isStale,
        commitOffsetsIfNecessary,
      }) => {
        if (!isRunning() || isStale()) {
          // node.log(`Skip consuming, isRunning: ${isRunning()}, isStale: ${isStale()}, batchMessageLength: ${batch.messages.length}, resolveOffset: ${maxOffset}`);
          return;
        }
        try {
          node.send({
            topic: batch.topic,
            partition: batch.partition,
            payload: batch.messages,
          });

          let maxOffset = 0;
          if (batch.messages.length > 0) {
            maxOffset = batch.messages[batch.messages.length - 1].offset;
            resolveOffset(maxOffset);
            // // When `eachBatchAutoResolve = true`, the offset will be committed automatically
            // await commitOffsetsIfNecessary();
          }
          await heartbeat();
          // node.log(`Heartbeat, batchMessageLength: ${batch.messages.length}, resolveOffset: ${maxOffset}`);

          // Only show the first message received status
          if (!this.firstMessageReceived) {
            node.status({ fill: 'green', shape: 'dot', text: 'First message received' });
            this.firstMessageReceived = true;
          }
        } catch (ex) {
          node.onError(`Kafka Batch Consumer Error (batch message processing): ${ex}`, ex);
          node.status({ fill: 'red', shape: 'ring', text: 'Error' });
        }
      };
    } else {
      runOptions.eachBatch = async ({
        batch,
        resolveOffset,
        heartbeat,
        isRunning,
        isStale,
        commitOffsetsIfNecessary,
      }) => {
        if (!isRunning() || isStale()) {
          return;
        }
        try {
          const messageList = [];
          let count = 0;
          for (const message of batch.messages) {
            let msg = {
              offset: message.offset,
              key: convertMessage(message.key, config.keytype),
              value: convertMessage(message.value, config.valuetype),
              headers: Object.keys(message.headers).length === 0 ? null : {},
            };
            for (const [key, value] of Object.entries(message.headers)) {
              msg.headers[key] = value.toString();
            }
            messageList.push(msg);

            // Resolve and commit offset
            if (config.commitsize > 0 && ++count >= config.commitsize) {
              // resolveOffset(message.offset);
              // // When `eachBatchAutoResolve = true`, the offset will be committed automatically
              // await commitOffsetsIfNecessary();
              await heartbeat();
              count = 0;
            }
          }

          // Just for efficiency, ignore transaction safety
          node.send({
            topic: batch.topic,
            partition: batch.partition,
            payload: messageList,
          });

          // Resolve offset
          if (batch.messages.length > 0) {
            resolveOffset(batch.messages[batch.messages.length - 1].offset);
            // // When `eachBatchAutoResolve = true`, the offset will be committed automatically
            // await commitOffsetsIfNecessary();
          }
          await heartbeat();

          node.status({ fill: 'green', shape: 'dot', text: 'Message received' });
        } catch (ex) {
          node.onError(`Kafka Batch Consumer Error (batch message processing): ${ex}`, ex);
          node.status({ fill: 'red', shape: 'ring', text: 'Error' });
        }
      };
    }

    // Options Use: Advanced Options
    if (config.advancedoptions) {
      consumerOptions.sessionTimeout = config.sessiontimeout;
      consumerOptions.rebalanceTimeout = config.rebalancetimeout;
      consumerOptions.heartbeatInterval = config.heartbeatinterval;
      consumerOptions.metadataMaxAge = config.metadatamaxage;
      consumerOptions.allowAutoTopicCreation = config.allowautotopiccreation;
      consumerOptions.maxBytesPerPartition = config.maxbytesperpartition;
      consumerOptions.minBytes = config.minbytes;
      consumerOptions.maxBytes = config.maxbytes;
      consumerOptions.maxWaitTimeInMs = config.maxwaittimeinms;

      subscribeOptions.fromBeginning = config.frombeginning == null ? false : config.frombeginning;

      runOptions.autoCommitInterval = config.autocommitinterval;
      runOptions.autoCommitThreshold = config.autocommitthreshold;
    }

    // Auto resolve and commit offset
    // runOptions.autoCommit = true;
    runOptions.eachBatchAutoResolve = true;

    this.onConnect = function () {
      node.status({ fill: 'green', shape: 'ring', text: 'Ready' });
    };

    this.onDisconnect = function () {
      node.status({ fill: 'red', shape: 'ring', text: 'Offline' });
    };

    this.onRequestTimeout = function () {
      node.error('Kafka Batch Consumer Timeout');
      node.status({ fill: 'red', shape: 'ring', text: 'Timeout' });
    };

    this.onError = function (ex) {
      node.error(`Kafka Batch Consumer Error: ${ex}`, ex);
      node.status({ fill: 'red', shape: 'ring', text: 'Error' });
    };

    this.init = async function init() {
      node.consumer = broker.server.consumer(consumerOptions);
      node.status({ fill: 'yellow', shape: 'ring', text: 'Initializing' });

      const { CONNECT, DISCONNECT, REQUEST_TIMEOUT } = node.consumer.events;

      node.consumer.on(CONNECT, node.onConnect);
      node.consumer.on(DISCONNECT, node.onDisconnect);
      node.consumer.on(REQUEST_TIMEOUT, node.onRequestTimeout);

      await node.consumer.connect();
      await node.consumer.subscribe(subscribeOptions);
      await node.consumer.run(runOptions);
      node.send([null, { payload: { topic: subscribeOptions.topic, groupId: consumerOptions.groupId } }]);
    };

    this.init()
      .catch((e) => {
        node.status({ fill: 'red', shape: 'ring', text: 'Init error' });
        node.error(`Kafka Batch Consumer Init error: ${e}`, e);
      });

    node.on('close', function (done) {
      node.consumer
        .disconnect()
        .then(() => {
          node.status({ fill: 'grey', shape: 'ring', text: 'Disconnected' });

          if (done) {
            done();
          }
        })
        .catch((ex) => {
          if (done) {
            done(ex);
          } else {
            node.onError(ex);
          }
        });
    });
  }
  RED.nodes.registerType('kafkajs-batch-consumer', KafkajsBatchConsumerNode);
};
