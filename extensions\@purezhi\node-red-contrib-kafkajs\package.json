{"name": "@purezhi/node-red-contrib-kafkajs", "version": "0.1.1", "description": "A package to comunicate with a Kafka server and lets the user control the flow of message pipeline to control success and failure on message synchronization.", "homepage": "https://github.com/purezhi/node-red-contrib-kafkajs", "keywords": ["node-red", "kafka", "broker", "streaming", "consumer", "producer", "topic", "kafksjs"], "author": "purezhi", "license": "GPL-3.0", "private": false, "repository": {"type": "git", "url": "https://github.com/purezhi/node-red-contrib-kafkajs"}, "bugs": {"url": "https://github.com/purezhi/node-red-contrib-kafkajs/issues"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "pack": "npm pack --pack-destination tmp/", "start": "node node_modules/node-red/red.js -v -u ./data/profile", "debug": "node --nolazy node_modules/node-red/red.js -v -u ./data/profile", "start2": "node node_modules/node-red/red.js -v -u ./data/profile2", "debug2": "node --nolazy node_modules/node-red/red.js -v -u ./data/profile2", "start3": "node node_modules/node-red/red.js -v -u ./data/profile3", "debug3": "node --nolazy node_modules/node-red/red.js -v -u ./data/profile3"}, "node-red": {"version": ">=4.0.0", "nodes": {"kafkajs-broker": "src/kafkajs/broker.js", "kafkajs-producer": "src/kafkajs/producer.js", "kafkajs-consumer": "src/kafkajs/consumer.js", "kafkajs-batch-consumer": "src/kafkajs/batch-consumer.js"}}, "dependencies": {"kafkajs": "^2.2.4"}, "devDependencies": {"@purezhi/node-red-contrib-kafkajs": "file:", "@types/node-red": "^1.3.5", "node-red": "^4.0.9"}, "engines": {"node": ">=20.0.0"}, "volta": {"node": "20.19.2"}}