/**
 * JavaScript 的 polyfill 库【废弃】
 */

/**
 * 封装序列化
 * @param {*} map 
 * @param {*} space 
 * @returns 
 */
function mapToJSON(map, space) {
    return JSON.stringify(mapToSerializable(map), null, space);
}
function mapToSerializable(obj) {
    if (node != null) {
        node.error(obj);
    }
    if (obj instanceof Map) {
        return {
            __type: 'Map',
            entries: Array.from(obj.entries()).map(
                ([k, v]) => [k, mapToSerializable(v)]
            ),
        };
    } else if (Array.isArray(obj)) {
        return obj.map(mapToSerializable);
    } else if (obj && typeof obj === 'object') {
        const res = {};
        for (const [k, v] of Object.entries(obj)) {
            res[k] = mapToSerializable(v);
        }
        return res;
    }
    return obj;
}

/**
 * 封装反序列化
 * @param {*} str 
 * @returns 
 */
function mapFromJSON(str) {
    return mapFromSerializable(JSON.parse(str));
}
function mapFromSerializable(obj) {
    if (node != null) {
        node.error(obj);
    }
    if (
        obj &&
        typeof obj === 'object' &&
        obj.__type === 'Map' &&
        Array.isArray(obj.entries)
    ) {
        return new Map(obj.entries.map(
            ([k, v]) => [k, mapFromSerializable(v)]
        ));
    } else if (Array.isArray(obj)) {
        return obj.map(mapFromSerializable);
    } else if (obj && typeof obj === 'object') {
        const res = {};
        for (const [k, v] of Object.entries(obj)) {
            res[k] = mapFromSerializable(v);
        }
        return res;
    }
    return obj;
}

/**
 * 针对 JavaScript Map 做 JSON.stringify 时的 replacer
 * 如果 value instanceof Map，就把它替换成 { __type: 'Map', entries: [...] }
 * @param {*} key 
 * @param {*} value 
 * @returns 
 */
function mapReplacer(key, value) {
    if (value instanceof Map) {
        return {
            __type: 'Map',
            entries: Array.from(value.entries()),
        };
    }
    return value;
}

/**
 * 针对 JSON.parse 时的 reviver
 * 如果遇到 { __type: 'Map', entries: [...] }，就重建成 Map 实例
 * @param {*} key 
 * @param {*} value 
 * @returns 
 */
function mapReviver(key, value) {
    if (
        value !== null &&
        typeof value === 'object' &&
        value.__type === 'Map' &&
        Array.isArray(value.entries)
    ) {
        return new Map(value.entries);
    }
    return value;
}

module.exports = {
    mapToJSON,
    mapFromJSON,
    // mapToSerializable,
    // mapFromSerializable,
    // mapReplacer,
    // mapReviver,
};
