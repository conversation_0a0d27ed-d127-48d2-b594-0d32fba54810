<script type="text/javascript">
  RED.nodes.registerType('kafkajs-broker', {
    category: 'config',
    defaults: {
      name: { required: false },
      brokers: { value: [], required: true },
      clientid: { required: true },
      connectiontimeout: { value: 3000, required: true },
      requesttimeout: { value: 25000, required: true },
      loglevel: { value: 'error', required: true },

      auth: { value: 'none', required: true },

      tlscacert: { required: false },
      tlsclientcert: { required: false },
      tlsprivatekey: { required: false },
      tlspassphrase: { required: false },

      saslmechanism: { value: 'plain', required: false },
      credsfromsettings: { value: false, required: false },
      saslssl: { value: true, required: false },
			saslselfsign: { value: false, required: false },

      advancedretry: { value: false, required: true },

      maxretrytime: { value: 30000, required: true },
      initialretrytime: { value: 300, required: true },
      factor: { value: 0.2, required: true },
      multiplier: { value: 2, required: true },
      retries: { value: 5, required: true },
    },
    credentials: {
      saslusername: { type: 'text', required: false },
      saslpassword: { type: 'password', required: false },
    },
    label: function () {
      return this.name || 'kafkajs-broker';
    },

    oneditprepare: function () {
      function checkAdvancedRetry(checked) {
        if (checked) {
          $('#node-config-advancedretry').show();
        } else {
          $('#node-config-advancedretry').hide();
        }
      }

      function checkAuthMethod(val) {
        if (val == 'tls') {
          $('#node-config-sasl').hide();
          $('#node-config-tls').show();
        } else if (val == 'sasl') {
          $('#node-config-tls').hide();
          $('#node-config-sasl').show();
        } else {
          $('#node-config-sasl').hide();
          $('#node-config-tls').hide();
        }
      }

      function checkUsernamePassword(checked) {
        if (checked) {
          $('#node-config-saslusername').hide();
          $('#node-config-saslpassword').hide();
        } else {
          $('#node-config-saslusername').show();
          $('#node-config-saslpassword').show();
        }
      }

      checkAdvancedRetry($('#node-config-input-advancedretry').is(':checked'));
      checkAuthMethod($('#node-config-input-auth').val());
      checkUsernamePassword($('#node-config-input-credsfromsettings').is(':checked'));

      $('#node-config-input-advancedretry').change(function () {
        checkAdvancedRetry($(this).is(':checked'));
      });

      $('#node-config-input-auth').change(function () {
        checkAuthMethod($(this).val());
      });

      $('#node-config-input-credsfromsettings').change(function () {
        checkUsernamePassword($(this).is(':checked'));
      });
    },
  });
</script>

<script type="text/html" data-template-name="kafkajs-broker">
  <style>
    .node-red-kafka-config .fa {
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
    }
    .node-red-kafka-config label i.fa:first-child {
      margin-right: 4px;
    }
    .node-red-kafka-config .checkbox-aligned {
      display: inline-block;
      width: auto;
      vertical-align: top;
    }
    .node-red-kafka-config .inner-form {
      margin-left: 12px;
      padding-left: 12px;
      border-left: 1px solid #dfdfdf;
    }
    .red-ui-editor .node-red-kafka-config .form-row {
      display: flex;
    }
    .red-ui-editor .node-red-kafka-config .form-row label {
      width: 180px;
    }
    .red-ui-editor .node-red-kafka-config .form-row input,
    .red-ui-editor .node-red-kafka-config .form-row select {
      width: auto;
      flex: 1 1 auto;
    }
    .red-ui-editor .node-red-kafka-config .form-row input[type=checkbox] {
      flex: 0.1 1 auto;
    }
  </style>

  <div class="node-red-kafka-config">
    <div class="form-row">
      <label for="node-config-input-name"><i class="fa fa-tag"></i>Name</label>
      <input id="node-config-input-name" type="text" placeholder="Name" />
    </div>
    <div class="form-row">
      <label for="node-config-input-brokers"><i class="fa fa-server"></i>Brokers</label>
      <input id="node-config-input-brokers" type="text" placeholder="example1.com:9092,example2.com:9092,..." />
    </div>
    <div class="form-row">
      <label for="node-config-input-clientid"><i class="fa fa-id-card-o"></i>Client ID</label>
      <input id="node-config-input-clientid" type="text" placeholder="Client ID" />
    </div>
    <div class="form-row">
      <label for="node-config-input-connectiontimeout" title="Connection Timeout"><i class="fa fa-clock-o"></i>Con. Timeout</label>
      <input id="node-config-input-connectiontimeout" type="number" step="1" min="1" />
    </div>
    <div class="form-row">
      <label for="node-config-input-requesttimeout" title="Request Timeout"><i class="fa fa-clock-o"></i>Req. Timeout</label>
      <input id="node-config-input-requesttimeout" type="number" step="1" min="1" />
    </div>
    <div class="form-row">
      <label for="node-config-input-loglevel"><i class="fa fa-file-text-o"></i>Log Level</label>
      <select id="node-config-input-loglevel">
        <option value="nothing">NOTHING</option>
        <option value="error">ERROR</option>
        <option value="warn">WARN</option>
        <option value="info">INFO</option>
        <option value="debug">DEBUG</option>
      </select>
    </div>
    <div class="form-row">
      <label for="node-config-input-auth"><i class="fa fa-file-text-o"></i>Auth</label>
      <select id="node-config-input-auth">
        <option value="none">NONE</option>
        <option value="tls">TLS</option>
        <option value="sasl">SASL</option>
      </select>
    </div>
    <div id="node-config-tls" class="inner-form">
      <div class="form-row">
        <label for="node-config-input-tlscacert"><i class="fa fa-certificate"></i>CA Cert</label>
        <input id="node-config-input-tlscacert" type="text" placeholder="/var/..." />
      </div>

      <div class="form-row">
        <label for="node-config-input-tlsclientcert"><i class="fa fa-certificate"></i>Client Cert</label>
        <input id="node-config-input-tlsclientcert" type="text" placeholder="/var/..." />
      </div>

      <div class="form-row">
        <label for="node-config-input-tlsprivatekey"><i class="fa fa-tag"></i>Private Key</label>
        <input id="node-config-input-tlsprivatekey" type="text" placeholder="/var/..." />
      </div>

      <div class="form-row">
        <label for="node-config-input-tlspassphrase"><i class="fa fa-key"></i>Passphrase</label>
        <input id="node-config-input-tlspassphrase" type="password" placeholder="Passphrase" />
      </div>

    </div>
    <div id="node-config-sasl" class="inner-form">
      <div class="form-row">
        <label for="node-config-input-saslmechanism"><i class="fa fa-lock"></i>Mechanism</label>
        <select id="node-config-input-saslmechanism">
          <option value="plain">PLAIN</option>
          <option value="scram-sha-256">SCRAM-SHA-256</option>
          <option value="scram-sha-512">SCRAM-SHA-512</option>
        </select>
      </div>
      <div class="form-row">
        <label for="node-config-input-credsfromsettings"><i class="fa fa-cogs"></i>Settings credentials</label>
        <input type="checkbox" id="node-config-input-credsfromsettings" class="checkbox-aligned" />
      </div>
      <div id="node-config-saslusername" class="form-row">
        <label for="node-config-input-saslusername"><i class="fa fa-id-card-o"></i>Username</label>
        <input id="node-config-input-saslusername" type="text" placeholder="Username" />
      </div>
      <div id="node-config-saslpassword" class="form-row">
        <label for="node-config-input-saslpassword"><i class="fa fa-key"></i>Password</label>
        <input id="node-config-input-saslpassword" type="password" placeholder="Password" />
      </div>
      <div class="form-row">
        <label for="node-config-input-saslssl"><i class="fa fa-expeditedssl"></i>Use SSL</label>
        <input type="checkbox" id="node-config-input-saslssl" class="checkbox-aligned" />
      </div>
      <div class="form-row">
        <label for="node-config-input-saslselfsign"><i class="fa fa-certificate"></i>Self-signed certificates</label>
        <input type="checkbox" id="node-config-input-saslselfsign" />
      </div>
    </div>
    <div class="form-row">
      <label for="node-config-input-advancedretry" class="checkbox-aligned"><i class="fa fa-ellipsis-v"></i>Advanced Options</label>
      <input id="node-config-input-advancedretry" type="checkbox" style="width:30%" />
    </div>
    <div id="node-config-advancedretry" class="inner-form">
      <div class="form-row">
        <label for="node-config-input-maxretrytime" title="Maximum Retry Time"><i class="fa fa-clock-o"></i>Max. Retry (ms)</label>
        <input id="node-config-input-maxretrytime" type="number" step="1" min="1" />
      </div>
      <div class="form-row">
        <label for="node-config-input-initialretrytime" title="Initial Retry Time"><i class="fa fa-clock-o"></i>Init. Retry (ms)</label>
        <input id="node-config-input-initialretrytime" type="number" step="1" min="1" />
      </div>
      <div class="form-row">
        <label for="node-config-input-factor"><i class="fa fa-calculator"></i>Factor</label>
        <input id="node-config-input-factor" type="number" step="0.1" min="0" />
      </div>
      <div class="form-row">
        <label for="node-config-input-multiplier"><i class="fa fa-calculator"></i>Multiplier</label>
        <input id="node-config-input-multiplier" type="number" step="1" min="1" />
      </div>
      <div class="form-row">
        <label for="node-config-input-retries"><i class="fa fa-refresh"></i>Retries</label>
        <input id="node-config-input-retries" type="number" step="1" min="1" />
      </div>
    </div>
  </div>
</script>

<script type="text/html" data-help-name="kafkajs-broker">
  <h3>Settings credentials</h3>
  <p>
    If this is checked, variables specified in the settings.js file will be used for the username and password to connect to the broker (Only for SASL authentication)
  </p>
  <p>
    The field names used for username and password are:
    <br />
    <strong>PZ_CONTRIB_KAFKA_USERNAME</strong> and
    <br />
    <strong>PZ_CONTRIB_KAFKA_PASSWORD</strong> respectively.
  </p>
  <p>
    They can be added to the settings.js file along with the other fields:
  </p>
    <pre>
module.exports = {
  ...
  PZ_CONTRIB_KAFKA_USERNAME: 'admin',
  PZ_CONTRIB_KAFKA_PASSWORD: 'password',
  ...
}
    </pre>

  <p>
    A good setup would be:
    <ol>
      <li>Use environment variables with the <em>dotenv</em> npm package to specify your credentials.</li>
      <li>In the settings file set them as exports like in the above example.</li>
      <li>Set them to empty strings in the environment like this: `process.env.PZ_CONTRIB_KAFKA_PASSWORD = ''`.</li>
    </ol>
    The last step is very important because environment variables are accessible via the front end and can be accessed by any user.
  </p>
</script>
