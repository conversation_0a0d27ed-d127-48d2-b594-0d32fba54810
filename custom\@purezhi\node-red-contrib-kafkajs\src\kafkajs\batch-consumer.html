<script type="text/javascript">
  RED.nodes.registerType('kafkajs-batch-consumer', {
    category: 'network',
    defaults: {
      name: { value: '', required: false },
      broker: { value: '', required: true, type: 'kafkajs-broker' },
      groupid: { value: '', required: false },
      topic: { value: '', required: true },
      sendrawmessage: { value: true, required: true },
      keytype: { value: 'buffer', required: true },
      valuetype: { value: 'buffer', required: true },
      commitsize: { value: 0, required: true },

      // Options Properties: Advanced Options
      advancedoptions: { value: false },
      autocommitinterval: { value: 5000, required: true },
      autocommitthreshold: { value: 100, required: true },
      sessiontimeout: { value: 30000, required: true },
      rebalancetimeout: { value: 60000, required: true },
      heartbeatinterval: { value: 3000, required: true },
      metadatamaxage: { value: 300000, required: true },
      maxbytesperpartition: { value: 1048576, required: true },
      minbytes: { value: 1, required: true },
      maxbytes: { value: 10485760, required: true },
      maxwaittimeinms: { value: 5000, required: true },
      frombeginning: { value: false, required: true },
    },
    inputs: 0,
    outputs: 2,
    outputLabels: ['New message', 'Subscribed'],
    color: '#A6BBCF',
    icon: 'Kafka.svg',
    align: 'left',
    label: function () {
      return this.name || 'kafkajs-batch-consumer';
    },
    paletteLabel: function() {
      return 'kafka batch in';
    },
    labelStyle: function () {
      return 'node_label_italic';
    },
    oneditprepare: function () {
      // Options UI: sendRawMessage
      function checkSendRawMessage(checked) {
        if (checked) {
          $('#node-sendrawmessage').hide();
        } else {
          $('#node-sendrawmessage').show();
        }
      }
      checkSendRawMessage($('#node-input-sendrawmessage').val());
      $('#node-input-sendrawmessage').change(function () {
        checkSendRawMessage($(this).is(':checked'));
      });

      // Options UI: Advanced Options
      function checkAdvancedOptions(checked) {
        if (checked) {
          $('#node-advancedoptions').show();
        } else {
          $('#node-advancedoptions').hide();
        }
      }
      checkAdvancedOptions($('#node-input-advancedoptions').val());
      $('#node-input-advancedoptions').change(function () {
        checkAdvancedOptions($(this).is(':checked'));
      });
    },
  });
</script>

<script type="text/html" data-template-name="kafkajs-batch-consumer">
  <style>
    .node-red-kafka-config .flex-fill {
      flex: 1 0 auto;
    }
    .node-red-kafka-config .fa {
      display: inline-block;
      width: 16px;
      height: 16px;
      text-align: center;
    }
    .node-red-kafka-config label i.fa:first-child {
      margin-right: 4px;
    }
    .node-red-kafka-config .checkbox-aligned {
      display: inline-block;
      width: auto;
      vertical-align: top;
    }
    .node-red-kafka-config .inner-form {
      margin-left: 12px;
      padding-left: 12px;
      border-left: 1px solid #dfdfdf;
    }
    .red-ui-editor .node-red-kafka-config .form-row {
      display: flex;
    }
    .red-ui-editor .node-red-kafka-config .form-row label {
      width: 180px;
    }
    .red-ui-editor .node-red-kafka-config .form-row input,
    .red-ui-editor .node-red-kafka-config .form-row .red-ui-editableList,
    .red-ui-editor .node-red-kafka-config .form-row select {
      width: auto;
      flex: 1 1 auto;
    }
    .red-ui-editor .node-red-kafka-config .form-row input[type='checkbox'] {
      flex: 0.1 1 auto;
    }
    .red-ui-editor .node-red-kafka-config .message-header {
      display: flex;
    }
    .red-ui-editor .node-red-kafka-config .message-header > * {
      width: auto;
      flex: 1 1 auto;
    }
  </style>

  <div class="node-red-kafka-config">
    <div class="form-row">
      <label for="node-input-name"><i class="fa fa-tag"></i>Name</label>
      <input type="text" id="node-input-name" placeholder="Name" />
    </div>
    <div class="form-row">
      <label for="node-input-broker"><i class="fa fa-plug"></i>Broker</label>
      <input type="text" id="node-input-broker" />
    </div>
    <div class="form-row">
      <label for="node-input-name"><i class="fa fa-tag"></i>Group ID</label>
      <input type="text" id="node-input-groupid" placeholder="Group ID" />
    </div>
    <div class="form-row">
      <label for="node-input-topic"><i class="fa fa-link"></i>Topic</label>
      <input type="text" id="node-input-topic" placeholder="Topic" />
    </div>
    <!-- Options Title: sendRawMessage -->
    <div class="form-row">
      <label for="node-input-sendrawmessage" class="checkbox-aligned"><i class="fa fa-cogs"></i>Send raw message</label>
      <input id="node-input-sendrawmessage" type="checkbox" />
    </div>
    <!-- Options Content: sendRawMessage -->
    <div id="node-sendrawmessage" class="inner-form">
      <div class="form-row">
        <label for="node-input-keytype" class="checkbox-aligned"><i class="fa fa-braille"></i>Serialize key as</label>
        <select id="node-input-keytype">
          <option value="buffer">Buffer</option>
          <option value="json">JSON</option>
          <option value="string">String</option>
          <option value="boolean">Boolean</option>
          <option value="int8">Int8</option>
          <option value="int16be">Int16_BE</option>
          <option value="int16le">Int16_LE</option>
          <option value="int32be">Int32_BE</option>
          <option value="int32le">Int32_LE</option>
          <option value="int64be">Int64_BE</option>
          <option value="int64le">Int64_LE</option>
          <option value="uint8">UInt8</option>
          <option value="uint16be">UInt16_BE</option>
          <option value="uint16le">UInt16_LE</option>
          <option value="uint32be">UInt32_BE</option>
          <option value="uint32le">UInt32_LE</option>
          <option value="uint64be">UInt64_BE</option>
          <option value="uint64le">UInt64_LE</option>
          <option value="doublebe">Double_BE</option>
          <option value="doublele">Double_LE</option>
          <option value="floatbe">Float_BE</option>
          <option value="floatle">Float_LE</option>
        </select>
      </div>
      <div class="form-row">
        <label for="node-input-valuetype" class="checkbox-aligned"><i class="fa fa-braille"></i>Serialize value as</label>
        <select id="node-input-valuetype">
          <option value="buffer">Buffer</option>
          <option value="json">JSON</option>
          <option value="string">String</option>
          <option value="boolean">Boolean</option>
          <option value="int8">Int8</option>
          <option value="int16be">Int16_BE</option>
          <option value="int16le">Int16_LE</option>
          <option value="int32be">Int32_BE</option>
          <option value="int32le">Int32_LE</option>
          <option value="int64be">Int64_BE</option>
          <option value="int64le">Int64_LE</option>
          <option value="uint8">UInt8</option>
          <option value="uint16be">UInt16_BE</option>
          <option value="uint16le">UInt16_LE</option>
          <option value="uint32be">UInt32_BE</option>
          <option value="uint32le">UInt32_LE</option>
          <option value="uint64be">UInt64_BE</option>
          <option value="uint64le">UInt64_LE</option>
          <option value="doublebe">Double_BE</option>
          <option value="doublele">Double_LE</option>
          <option value="floatbe">Float_BE</option>
          <option value="floatle">Float_LE</option>
        </select>
      </div>
      <div class="form-row">
        <label for="node-input-commitsize"><i class="fa fa-clock-o"></i>Commit Size</label>
        <input id="node-input-commitsize" type="number" />
      </div>
    </div>

    <!-- Options Title: Advanced Options -->
    <div class="form-row">
      <label for="node-input-advancedoptions" class="checkbox-aligned"><i class="fa fa-cogs"></i>Advanced Options</label>
      <input id="node-input-advancedoptions" type="checkbox" />
    </div>
    <!-- Options Content: Advanced Options -->
    <div id="node-advancedoptions" class="inner-form">
      <div class="form-row">
        <label for="node-input-autocommitinterval"><i class="fa fa-clock-o"></i>Autocommit Interval</label>
        <input id="node-input-autocommitinterval" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-autocommitthreshold"><i class="fa fa-tag"></i>Autocommit Threshold</label>
        <input id="node-input-autocommitthreshold" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-sessiontimeout"><i class="fa fa-clock-o"></i>Session Timout (ms)</label>
        <input id="node-input-sessiontimeout" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-rebalancetimeout"><i class="fa fa-clock-o"></i>Rebalance Timeout</label>
        <input id="node-input-rebalancetimeout" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-heartbeatinterval"><i class="fa fa-clock-o"></i>Heartbeat Interval</label>
        <input id="node-input-heartbeatinterval" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-metadatamaxage"><i class="fa fa-clock-o"></i>Metadata Max Age</label>
        <input id="node-input-metadatamaxage" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-maxbytesperpartition"><i class="fa fa-tag"></i>Max Bytes Per Partition</label>
        <input id="node-input-maxbytesperpartition" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-minbytes"><i class="fa fa-tag"></i>Min Bytes</label>
        <input id="node-input-minbytes" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-maxbytes"><i class="fa fa-tag"></i>Max Bytes</label>
        <input id="node-input-maxbytes" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-maxwaittimeinms"><i class="fa fa-tag"></i>Max Wait (ms)</label>
        <input id="node-input-maxwaittimeinms" type="number" />
      </div>
      <div class="form-row">
        <label for="node-input-frombeginning"><i class="fa fa-step-backward"></i>From Beginning</label>
        <input type="checkbox" id="node-input-frombeginning" />
      </div>
    </div>
  </div>
</script>

<script type="text/html" data-help-name="kafkajs-batch-consumer">
  <p>Definition of kafkajs-batch-consumer</p>
  <p>Usage details can be found under https://github.com/purezhi/node-red-contrib-kafkajs/blob/README.md</p>
</script>
