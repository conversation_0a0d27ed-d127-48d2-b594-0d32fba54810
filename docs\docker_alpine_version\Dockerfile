# FROM node:20-alpine
# FROM dockerhub.icu/library/node:20-alpine
FROM harbor2.qdbdtd.com:8088/middleware/node:20-alpine
LABEL maintainer="<PERSON><wang<PERSON><EMAIL>>"

# System update and timezone config
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
  && apk update \
  && apk upgrade \
  && apk add --no-cache busybox busybox-extras logrotate tzdata \
  && ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
  && echo "Asia/Shanghai" > /etc/timezone

# Setup system logrotation
COPY ./docker/pm2-apps /etc/logrotate.d/
# Debug: /usr/sbin/logrotate -d /etc/logrotate.d/pm2-apps
RUN chmod 644 /etc/logrotate.d/pm2-apps \
  && mkdir -p /var/lib/logrotate \
  && echo "*/1 * * * * /usr/sbin/logrotate /etc/logrotate.d/pm2-apps --state /var/lib/logrotate/status" >> /etc/crontabs/root

# Setup entrypoint
RUN mv /usr/local/bin/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh.bak
COPY ./docker/docker-entrypoint.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Config npm and install PM2 and Logrotation plugin
ENV NPM_CONFIG_LOGLEVEL=warn
# npm config set registry https://registry.npmmirror.com \
RUN npm install -g --unsafe-perm node-red@4.0.9 \
  && npm install -g pm2

# Bundle APP files
WORKDIR /app
COPY . .

# Install extension nodes
WORKDIR /app/custom/@bdtd/js-common-polyfill
RUN npm install --omit=dev --legacy-peer-deps
WORKDIR /app/custom/@purezhi/node-red-contrib-kafkajs
RUN npm install --omit=dev --legacy-peer-deps

WORKDIR /app/profile
RUN cp package-sample.json package.json \
  && npm install --omit=dev --legacy-peer-deps \
  && npm install --unsafe-perm ../custom/@bdtd/js-common-polyfill \
  && npm install --unsafe-perm ../custom/@purezhi/node-red-contrib-kafkajs \
  && npm cache clean --force

# Expose the listening port of your app
EXPOSE 1880

# Start the app
WORKDIR /app
CMD [ "pm2-runtime", "start", "ecosystem.config.js" ]
