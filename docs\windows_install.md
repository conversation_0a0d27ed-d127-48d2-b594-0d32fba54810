# Windows 环境安装服务

1）安装 pm2，`npm install -g pm2`。

2）新建文件夹 `c:\etc\.pm2`，创建一个新的 `PM2_HOME` 变量（在系统级别，而不是用户级别）并设置值 `c:\etc\.pm2`，关闭所有打开的终端窗口（或重新启动 Windows）。确保您的 `PM2_HOME` 已正确设置，运行 `echo %PM2_HOME%`。

3）在 node-gw 文件夹执行 `pm2 reload ecosystem.config.js --env=production`，检查您的应用程序是否按预期运行。如果一切正常，保存您的配置： `pm2 save`。

4）使用节点模块 pm2-windows-service 创建一个 Windows 服务，执行 `npm install -g pm2-windows-service`。

5）以管理员方式打开终端执行 `pm2-service-install -n PM2`，配置参照如下：

> ? Perform environment setup (recommended)? Yes
> ? Set PM2_HOME? Yes
> ? PM2_HOME value (this path should be accessible to the service user and
> should not contain any “user-context” variables [e.g. %APPDATA%]): c:\etc\.pm2\
> ? Set PM2_SERVICE_SCRIPTS (the list of start-up scripts for pm2)? No
> ? Set PM2_SERVICE_PM2_DIR (the location of the global pm2 to use with the service)? [recommended] Yes
> ? Specify the directory containing the pm2 version to be used by the service C:\USERS\<USER>\APPDATA\ROAMING\NPM\node_modules\pm2\index.js
