
## 变更履历

+ 20240903 添加 `node-red-contrib-ssh-v3` 的依赖 `async-mutex`；

## 版本和节点管理

1）修改 package.json 和 Dockefile 升级 Node-RED 版本。

```json
    "scripts": {
        "start": "npm install -g --unsafe-perm node-red@<安装版本> && node-red --settings ./settings-dev.js",
        "test": "echo \"Error: no test specified\" && exit 1"
    },
```

```dockerfile
RUN npm config set registry https://registry.npmmirror.com \
  && npm install -g --unsafe-perm node-red@<安装版本> \
  && npm install -g pm2
```

2）修改 `./profile/package-template.json` 添加删除节点。

## 开发介绍

### Map 序列化

