# 设置所有文本文件默认使用自动换行符处理，并强制在仓库中使用 LF
* text=auto eol=lf

# 明确指定源代码和配置文件使用 LF
*.c text eol=lf
*.cpp text eol=lf
*.h text eol
*.hpp text eol=lf
*.py text eol=lf
*.java text eol
*.js text eol=lf
*.ts text eol=lf
*.json text eol=lf
*.yaml text eol
*.yaml text eol=lf
*.sh text eol=lf

# Windows 特定脚本使用 CRLF
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# 二进制文件，不处理换行符
*.jpg binary
*.png binary
*.gif binary
*.pdf binary
*.zip binary
*.tar.gz binary
*.exe binary

# 其他文件类型
*.md text eol=lf
*.txt text eol=lf
*.gitignore text eol=lf
*.Dockerfile text eol=lf